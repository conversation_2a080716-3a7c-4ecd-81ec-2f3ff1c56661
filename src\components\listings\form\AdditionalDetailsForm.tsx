
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { carBodyTypes, carFuelTypes, carTransmissionTypes, carColors } from '@/utils/dictionaries/vehicles';
import { useFormContext } from './ListingFormContext';

const AdditionalDetailsForm: React.FC = () => {
  const { t } = useLanguage();
  const { form } = useFormContext();

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">{t('vehicleSpecifications')}</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="bodyType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('bodyType')}</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('selectBodyType')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {/* Removed the empty value SelectItem */}
                  {carBodyTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="fuelType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('fuelType')}</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('selectFuelType')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {/* Removed the empty value SelectItem */}
                  {carFuelTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="transmission"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('transmission')}</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('selectTransmission')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {/* Removed the empty value SelectItem */}
                  {carTransmissionTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="color"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('color')}</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('selectColor')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {/* Removed the empty value SelectItem */}
                  {carColors.map((colorOption) => (
                    <SelectItem key={colorOption} value={colorOption}>
                      {colorOption}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="mileage"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('mileage')}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t('enterMileage')}
                  type="number"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};

export default AdditionalDetailsForm;
