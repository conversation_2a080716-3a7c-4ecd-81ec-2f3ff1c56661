import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertCircle, User, Mail, Lock } from 'lucide-react';
import { toast } from 'sonner';

interface TestResult {
  test: string;
  status: 'pending' | 'success' | 'error';
  message: string;
}

const RegistrationTest: React.FC = () => {
  const { signUp, signIn, signOut, isAuthenticated, user } = useAuth();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const updateTestResult = (test: string, status: 'success' | 'error', message: string) => {
    setTestResults(prev => {
      const existing = prev.find(r => r.test === test);
      if (existing) {
        existing.status = status;
        existing.message = message;
        return [...prev];
      }
      return [...prev, { test, status, message }];
    });
  };

  const runRegistrationTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    const testEmail = `test${Date.now()}@example.com`;
    const testPassword = 'testpassword123';
    const testFirstName = 'Test';
    const testLastName = 'User';

    try {
      // Test 1: Valid Registration
      updateTestResult('Valid Registration', 'pending', 'Testing user registration...');
      try {
        await signUp(testEmail, testPassword, testFirstName, testLastName);
        updateTestResult('Valid Registration', 'success', 'User registration successful');
      } catch (error: any) {
        updateTestResult('Valid Registration', 'error', `Registration failed: ${error.message}`);
      }

      // Wait a bit for the registration to process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Test 2: Sign In with New Account
      updateTestResult('Sign In', 'pending', 'Testing sign in with new account...');
      try {
        await signIn(testEmail, testPassword);
        updateTestResult('Sign In', 'success', 'Sign in successful');
      } catch (error: any) {
        updateTestResult('Sign In', 'error', `Sign in failed: ${error.message}`);
      }

      // Test 3: Check Authentication State
      updateTestResult('Authentication State', 'pending', 'Checking authentication state...');
      if (isAuthenticated && user) {
        updateTestResult('Authentication State', 'success', `User authenticated: ${user.email}`);
      } else {
        updateTestResult('Authentication State', 'error', 'User not authenticated after sign in');
      }

      // Test 4: Sign Out
      updateTestResult('Sign Out', 'pending', 'Testing sign out...');
      try {
        await signOut();
        updateTestResult('Sign Out', 'success', 'Sign out successful');
      } catch (error: any) {
        updateTestResult('Sign Out', 'error', `Sign out failed: ${error.message}`);
      }

      // Test 5: Invalid Email Format
      updateTestResult('Invalid Email Validation', 'pending', 'Testing invalid email validation...');
      try {
        await signUp('invalid-email', testPassword, testFirstName, testLastName);
        updateTestResult('Invalid Email Validation', 'error', 'Should have rejected invalid email');
      } catch (error: any) {
        updateTestResult('Invalid Email Validation', 'success', 'Invalid email properly rejected');
      }

      // Test 6: Short Password
      updateTestResult('Short Password Validation', 'pending', 'Testing short password validation...');
      try {
        await signUp(`test2${Date.now()}@example.com`, '123', testFirstName, testLastName);
        updateTestResult('Short Password Validation', 'error', 'Should have rejected short password');
      } catch (error: any) {
        updateTestResult('Short Password Validation', 'success', 'Short password properly rejected');
      }

      // Test 7: Missing Fields
      updateTestResult('Missing Fields Validation', 'pending', 'Testing missing fields validation...');
      try {
        await signUp(`test3${Date.now()}@example.com`, testPassword, '', testLastName);
        updateTestResult('Missing Fields Validation', 'error', 'Should have rejected missing fields');
      } catch (error: any) {
        updateTestResult('Missing Fields Validation', 'success', 'Missing fields properly rejected');
      }

    } catch (error: any) {
      console.error('Test suite error:', error);
      toast.error('Test suite failed', {
        description: error.message
      });
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-500">Success</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-6 w-6" />
          User Registration System Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <p className="text-muted-foreground">
            This test verifies the user registration and authentication system functionality.
          </p>
          <Button 
            onClick={runRegistrationTests} 
            disabled={isRunning}
            className="flex items-center gap-2"
          >
            {isRunning ? (
              <>
                <AlertCircle className="h-4 w-4 animate-spin" />
                Running Tests...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4" />
                Run Tests
              </>
            )}
          </Button>
        </div>

        {testResults.length > 0 && (
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Test Results</h3>
            <div className="space-y-2">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    {getStatusIcon(result.status)}
                    <div>
                      <p className="font-medium">{result.test}</p>
                      <p className="text-sm text-muted-foreground">{result.message}</p>
                    </div>
                  </div>
                  {getStatusBadge(result.status)}
                </div>
              ))}
            </div>

            <div className="mt-4 p-4 bg-muted rounded-lg">
              <h4 className="font-semibold mb-2">Test Summary</h4>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-green-500">
                    {testResults.filter(r => r.status === 'success').length}
                  </p>
                  <p className="text-sm text-muted-foreground">Passed</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-red-500">
                    {testResults.filter(r => r.status === 'error').length}
                  </p>
                  <p className="text-sm text-muted-foreground">Failed</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-yellow-500">
                    {testResults.filter(r => r.status === 'pending').length}
                  </p>
                  <p className="text-sm text-muted-foreground">Pending</p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
          <h4 className="font-semibold mb-2 flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Current Authentication Status
          </h4>
          <div className="space-y-2">
            <p>
              <strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}
            </p>
            {user && (
              <>
                <p>
                  <strong>User Email:</strong> {user.email}
                </p>
                <p>
                  <strong>User ID:</strong> {user.id}
                </p>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RegistrationTest;
