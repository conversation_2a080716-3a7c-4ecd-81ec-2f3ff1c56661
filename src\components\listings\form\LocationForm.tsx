
import React, { useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Combobox } from '@/components/ui/combobox';
import { cities } from '@/utils/dictionaries';
import { useFormContext } from './ListingFormContext';

const LocationForm: React.FC = () => {
  const { t, language } = useLanguage();
  const { form, customLocation, setCustomLocation } = useFormContext();
  const [locationOptions, setLocationOptions] = React.useState<string[]>([]);
  
  // Set location options based on language
  useEffect(() => {
    setLocationOptions(cities[language as keyof typeof cities] || cities.en);
  }, [language]);
  
  return (
    <FormField
      control={form.control}
      name="location"
      render={({ field }) => (
        <FormItem>
          <FormLabel>{t('location')} *</FormLabel>
          <FormControl>
            <Combobox
              value={field.value}
              setValue={field.onChange}
              options={locationOptions}
              placeholder={t('selectLocation')}
              allowCustomValue
              onCustomValueChange={setCustomLocation}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default LocationForm;
