import { supabase } from '@/integrations/supabase/client';
import { User } from '@supabase/supabase-js';

/**
 * Результат проверки административных прав
 */
export interface AdminCheckResult {
  isAdmin: boolean;
  error?: string;
  user?: User;
}

/**
 * Проверяет, является ли текущий пользователь администратором
 */
export const checkAdminAccess = async (): Promise<AdminCheckResult> => {
  try {
    // Получаем текущего пользователя
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      return {
        isAdmin: false,
        error: 'Authentication error: ' + userError.message,
      };
    }

    if (!user) {
      return {
        isAdmin: false,
        error: 'User not authenticated',
      };
    }

    // Проверяем права администратора через функцию базы данных
    const { data: isAdmin, error: adminError } = await supabase.rpc('is_user_admin', {
      user_id: user.id
    });

    if (adminError) {
      // Fallback: проверяем напрямую через таблицу profiles
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('is_admin, role')
        .eq('id', user.id)
        .single();

      if (profileError) {
        return {
          isAdmin: false,
          error: 'Error checking admin status: ' + profileError.message,
          user,
        };
      }

      const adminStatus = profileData?.is_admin === true || profileData?.role === 'admin';
      return {
        isAdmin: adminStatus,
        user,
      };
    }

    return {
      isAdmin: isAdmin === true,
      user,
    };
  } catch (error) {
    return {
      isAdmin: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Middleware для защиты административных операций
 */
export const requireAdmin = async (): Promise<AdminCheckResult> => {
  const result = await checkAdminAccess();
  
  if (!result.isAdmin) {
    throw new Error(result.error || 'Administrative access required');
  }
  
  return result;
};

/**
 * Обертка для административных операций с базой данных
 */
export const withAdminAccess = async <T>(
  operation: (user: User) => Promise<T>
): Promise<T> => {
  const { isAdmin, user, error } = await requireAdmin();
  
  if (!isAdmin || !user) {
    throw new Error(error || 'Administrative access required');
  }
  
  return await operation(user);
};

/**
 * Проверяет права доступа для конкретной административной операции
 */
export const checkAdminPermission = async (permission: string): Promise<boolean> => {
  const { isAdmin } = await checkAdminAccess();
  
  // В базовой реализации все административные разрешения требуют роль admin
  // В будущем можно расширить для более детальных разрешений
  return isAdmin;
};

/**
 * Типы административных разрешений
 */
export enum AdminPermission {
  MANAGE_USERS = 'manage_users',
  MANAGE_LISTINGS = 'manage_listings',
  MANAGE_REPORTS = 'manage_reports',
  MANAGE_SETTINGS = 'manage_settings',
  VIEW_ANALYTICS = 'view_analytics',
  MANAGE_NOTIFICATIONS = 'manage_notifications',
}

/**
 * Проверяет конкретное административное разрешение
 */
export const hasAdminPermission = async (permission: AdminPermission): Promise<boolean> => {
  return await checkAdminPermission(permission);
};

/**
 * Обертка для операций, требующих конкретного разрешения
 */
export const withAdminPermission = async <T>(
  permission: AdminPermission,
  operation: (user: User) => Promise<T>
): Promise<T> => {
  const hasPermission = await hasAdminPermission(permission);
  
  if (!hasPermission) {
    throw new Error(`Permission required: ${permission}`);
  }
  
  const { user } = await requireAdmin();
  if (!user) {
    throw new Error('User not found');
  }
  
  return await operation(user);
};

/**
 * Утилита для безопасного выполнения административных операций
 */
export const safeAdminOperation = async <T>(
  operation: () => Promise<T>,
  fallback?: T
): Promise<T | undefined> => {
  try {
    await requireAdmin();
    return await operation();
  } catch (error) {
    console.error('Admin operation failed:', error);
    return fallback;
  }
};

export default {
  checkAdminAccess,
  requireAdmin,
  withAdminAccess,
  checkAdminPermission,
  hasAdminPermission,
  withAdminPermission,
  safeAdminOperation,
  AdminPermission,
};
