
import React, { useEffect, useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useLanguage } from '@/hooks/useLanguage';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/integrations/supabase/client';
import BackButton from '@/components/BackButton';

// Message type definition
type Message = {
  id: string;
  created_at: string;
  sender_id: string;
  recipient_id: string;
  content: string;
  read: boolean;
  listing_id?: string;
  sender_name?: string;
  recipient_name?: string;
  listing_title?: string;
};

const Messages: React.FC = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('inbox');
  
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      toast.error(t('system.authRequired'), {
        description: t('system.loginToAccessMessages')
      });
      navigate('/auth');
    }
  }, [isAuthenticated, authLoading, navigate, t]);
  
  useEffect(() => {
    const fetchMessages = async () => {
      if (!user) return;
      
      try {
        setIsLoading(true);
        
        // Простой запрос сообщений без join'ов
        const { data: messagesData, error: messagesError } = await supabase
          .from('messages')
          .select('*')
          .or(`sender_id.eq.${user.id},recipient_id.eq.${user.id}`)
          .order('created_at', { ascending: false });
          
        if (messagesError) throw messagesError;

        // Получаем уникальные ID пользователей
        const userIds = new Set<string>();
        const listingIds = new Set<string>();
        
        messagesData.forEach(msg => {
          userIds.add(msg.sender_id);
          userIds.add(msg.recipient_id);
          if (msg.listing_id) listingIds.add(msg.listing_id);
        });

        // Получаем профили пользователей
        const { data: profiles } = await supabase
          .from('profiles')
          .select('id, username, full_name')
          .in('id', Array.from(userIds));

        // Получаем объявления
        const { data: listings } = listingIds.size > 0 ? await supabase
          .from('listings')
          .select('id, title')
          .in('id', Array.from(listingIds)) : { data: [] };
        
        // Создаем мап для быстрого поиска
        const profilesMap = new Map(profiles?.map(p => [p.id, p]) || []);
        const listingsMap = new Map(listings?.map(l => [l.id, l]) || []);
        
        // Обогащаем сообщения именами пользователей и названиями объявлений
        const processedMessages = messagesData.map((msg: any) => {
          const sender = profilesMap.get(msg.sender_id);
          const recipient = profilesMap.get(msg.recipient_id);
          const listing = msg.listing_id ? listingsMap.get(msg.listing_id) : null;
          
          return {
            ...msg,
            sender_name: sender?.full_name || sender?.username || 'Unknown User',
            recipient_name: recipient?.full_name || recipient?.username || 'Unknown User',
            listing_title: listing?.title || 'Unknown Listing'
          };
        });
        
        setMessages(processedMessages);
        
        // Mark received messages as read
        const unreadMessageIds = processedMessages
          .filter(msg => msg.recipient_id === user.id && !msg.read)
          .map(msg => msg.id);
          
        if (unreadMessageIds.length > 0) {
          await supabase
            .from('messages')
            .update({ read: true })
            .in('id', unreadMessageIds);
        }
        
      } catch (error) {
        console.error('Error fetching messages:', error);
        toast.error(t('system.errorLoadingMessages'));
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchMessages();
  }, [user, t]);
  
  const inboxMessages = messages.filter(msg => msg.recipient_id === user?.id);
  const sentMessages = messages.filter(msg => msg.sender_id === user?.id);
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };
  
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow pt-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-10">
          <BackButton to="/profile" />
          
          <div className="max-w-4xl mx-auto">
            <h1 className="text-2xl md:text-3xl font-bold mb-6">
              {t('system.messages')}
            </h1>
            
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="inbox">{t('system.inbox')}</TabsTrigger>
                <TabsTrigger value="sent">{t('system.sent')}</TabsTrigger>
              </TabsList>
              
              <TabsContent value="inbox">
                {isLoading ? (
                  <div className="text-center py-8">{t('system.loading')}</div>
                ) : inboxMessages.length > 0 ? (
                  <div className="space-y-4">
                    {inboxMessages.map(message => (
                      <div key={message.id} className="bg-card border rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <div className="font-medium">{t('system.from')}: {message.sender_name}</div>
                            <div className="text-sm text-muted-foreground">
                              {formatDate(message.created_at)}
                            </div>
                          </div>
                          {message.listing_id && (
                            <div className="text-sm">
                              {t('system.regarding')}: <a href={`/listings/${message.listing_id}`} className="text-primary hover:underline">
                                {message.listing_title}
                              </a>
                            </div>
                          )}
                        </div>
                        <div className="mt-2 p-3 bg-muted rounded-md">
                          {message.content}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">{t('system.noMessages')}</div>
                )}
              </TabsContent>
              
              <TabsContent value="sent">
                {isLoading ? (
                  <div className="text-center py-8">{t('system.loading')}</div>
                ) : sentMessages.length > 0 ? (
                  <div className="space-y-4">
                    {sentMessages.map(message => (
                      <div key={message.id} className="bg-card border rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <div className="font-medium">{t('system.to')}: {message.recipient_name}</div>
                            <div className="text-sm text-muted-foreground">
                              {formatDate(message.created_at)}
                            </div>
                          </div>
                          {message.listing_id && (
                            <div className="text-sm">
                              {t('system.regarding')}: <a href={`/listings/${message.listing_id}`} className="text-primary hover:underline">
                                {message.listing_title}
                              </a>
                            </div>
                          )}
                        </div>
                        <div className="mt-2 p-3 bg-muted rounded-md">
                          {message.content}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">{t('system.noSentMessages')}</div>
                )}
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default Messages;
