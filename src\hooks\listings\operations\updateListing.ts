
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Listing } from '../types';

export const useUpdateListing = (
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
  setListings: React.Dispatch<React.SetStateAction<Listing[]>>,
  setFeaturedListings: React.Dispatch<React.SetStateAction<Listing[]>>
) => {
  const updateListing = async (id: string, listing: Partial<Listing>) => {
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from('listings')
        .update(listing)
        .eq('id', id)
        .select()
        .single();
        
      if (error) {
        throw error;
      }
      
      setListings(prevListings => 
        prevListings.map(item => item.id === id ? data : item)
      );
      
      setFeaturedListings(prevFeatured => {
        const idx = prevFeatured.findIndex(item => item.id === id);
        if (idx >= 0) {
          const newFeatured = [...prevFeatured];
          newFeatured[idx] = data;
          return newFeatured;
        }
        return prevFeatured;
      });
      
      toast.success("Listing updated successfully", {
        description: "Your changes have been saved"
      });
      
      return data;
    } catch (error) {
      console.error('Error updating listing:', error);
      toast.error("Error updating listing", {
        description: "Failed to update listing"
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return { updateListing };
};
