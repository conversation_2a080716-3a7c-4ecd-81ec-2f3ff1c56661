import React, { useState, useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import EnhancedListingTable from './listings/EnhancedListingTable';
import BulkActions from './listings/BulkActions';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search, RefreshCw } from 'lucide-react';
import { ListingWithStatus, ListingStatus, BulkAction, BulkOperationResult } from '@/types/admin';

const EnhancedAdminListings: React.FC = () => {
  const { t } = useLanguage();
  const { toast } = useToast();
  
  const [listings, setListings] = useState<ListingWithStatus[]>([]);
  const [filteredListings, setFilteredListings] = useState<ListingWithStatus[]>([]);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    fetchListings();
  }, []);

  useEffect(() => {
    filterListings();
  }, [listings, searchTerm, statusFilter]);

  const fetchListings = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('listings')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      const listingsWithStatus: ListingWithStatus[] = data.map(listing => ({
        ...listing,
        status: listing.status || 'pending'
      }));

      setListings(listingsWithStatus);
    } catch (error) {
      console.error('Error fetching listings:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch listings',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filterListings = () => {
    let filtered = [...listings];

    if (searchTerm) {
      const lowerSearch = searchTerm.toLowerCase();
      filtered = filtered.filter(listing =>
        listing.title.toLowerCase().includes(lowerSearch) ||
        listing.make.toLowerCase().includes(lowerSearch) ||
        listing.model.toLowerCase().includes(lowerSearch) ||
        listing.location.toLowerCase().includes(lowerSearch)
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(listing => listing.status === statusFilter);
    }

    setFilteredListings(filtered);
    // Clear selection when filters change
    setSelectedIds([]);
  };

  const handleStatusChange = async (id: string, newStatus: ListingStatus) => {
    try {
      const { error } = await supabase
        .from('listings')
        .update({ status: newStatus })
        .eq('id', id);

      if (error) throw error;

      setListings(prev => prev.map(listing =>
        listing.id === id ? { ...listing, status: newStatus } : listing
      ));

      toast({
        title: t('admin.bulkSuccess'),
        description: `Listing status updated to ${newStatus}`
      });
    } catch (error) {
      console.error('Error updating status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update listing status',
        variant: 'destructive'
      });
    }
  };

  const handleBulkAction = async (action: BulkAction): Promise<void> => {
    if (selectedIds.length === 0) return;

    try {
      let updateData: Partial<ListingWithStatus> = {};
      let shouldDelete = false;

      switch (action) {
        case 'approve':
          updateData = { status: 'approved' };
          break;
        case 'reject':
          updateData = { status: 'rejected' };
          break;
        case 'block':
          updateData = { status: 'blocked' };
          break;
        case 'unblock':
          updateData = { status: 'active' };
          break;
        case 'delete':
          shouldDelete = true;
          break;
      }

      if (shouldDelete) {
        const { error } = await supabase
          .from('listings')
          .delete()
          .in('id', selectedIds);

        if (error) throw error;

        setListings(prev => prev.filter(listing => !selectedIds.includes(listing.id)));
      } else {
        const { error } = await supabase
          .from('listings')
          .update(updateData)
          .in('id', selectedIds);

        if (error) throw error;

        setListings(prev => prev.map(listing =>
          selectedIds.includes(listing.id) ? { ...listing, ...updateData } : listing
        ));
      }

      setSelectedIds([]);
      toast({
        title: t('admin.bulkSuccess'),
        description: `${action} operation completed for ${selectedIds.length} listings`
      });
    } catch (error) {
      console.error('Error performing bulk action:', error);
      toast({
        title: t('admin.bulkError'),
        description: 'Failed to perform bulk operation',
        variant: 'destructive'
      });
    }
  };

  const handleFeatureToggle = async (id: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('listings')
        .update({ featured: !currentStatus })
        .eq('id', id);

      if (error) throw error;

      setListings(prev => prev.map(listing =>
        listing.id === id ? { ...listing, featured: !currentStatus } : listing
      ));

      toast({
        title: t('admin.bulkSuccess'),
        description: `Listing ${currentStatus ? 'unfeatured' : 'featured'} successfully`
      });
    } catch (error) {
      console.error('Error toggling feature:', error);
      toast({
        title: 'Error',
        description: 'Failed to toggle feature status',
        variant: 'destructive'
      });
    }
  };

  const handleDelete = async (id: string) => {
    try {
      const { error } = await supabase
        .from('listings')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setListings(prev => prev.filter(listing => listing.id !== id));
      toast({
        title: t('admin.bulkSuccess'),
        description: 'Listing deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting listing:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete listing',
        variant: 'destructive'
      });
    }
  };

  const handleSelectAll = () => {
    setSelectedIds(filteredListings.map(listing => listing.id));
  };

  const handleDeselectAll = () => {
    setSelectedIds([]);
  };

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder={t('admin.searchPlaceholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder={t('admin.filterByStatus')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('admin.allStatuses')}</SelectItem>
            <SelectItem value="pending">{t('admin.pending')}</SelectItem>
            <SelectItem value="approved">{t('admin.approved')}</SelectItem>
            <SelectItem value="rejected">{t('admin.rejected')}</SelectItem>
            <SelectItem value="blocked">{t('admin.blocked')}</SelectItem>
            <SelectItem value="active">{t('admin.active')}</SelectItem>
          </SelectContent>
        </Select>

        <Button variant="outline" onClick={fetchListings} disabled={isLoading}>
          <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          {t('admin.refresh')}
        </Button>
      </div>

      {/* Bulk Actions */}
      <BulkActions
        selectedIds={selectedIds}
        onAction={handleBulkAction}
        isLoading={isLoading}
        onSelectAll={handleSelectAll}
        onDeselectAll={handleDeselectAll}
        totalCount={filteredListings.length}
      />

      {/* Listings Table */}
      <EnhancedListingTable
        listings={filteredListings}
        selectedIds={selectedIds}
        onSelectionChange={setSelectedIds}
        onStatusChange={handleStatusChange}
        onFeatureToggle={handleFeatureToggle}
        onDelete={handleDelete}
        isLoading={isLoading}
      />

      {/* Results Summary */}
      {filteredListings.length > 0 && (
        <div className="text-center text-sm text-muted-foreground">
          {t('admin.showing')} {filteredListings.length} {t('admin.of')} {listings.length} {t('admin.items')}
        </div>
      )}
    </div>
  );
};

export default EnhancedAdminListings;
