
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useLanguage } from '@/hooks/useLanguage';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from "sonner";
import ListingForm from '@/components/listings/ListingForm';
import BackButton from '@/components/BackButton';

const CreateListing: React.FC = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  
  // Check authentication
  useEffect(() => {
    if (!isAuthenticated) {
      toast.error("Authentication Required", {
        description: "You must be logged in to create a listing"
      });
      navigate('/auth');
    }
  }, [isAuthenticated, navigate]);
  
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow pt-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-10">
          <div className="max-w-3xl mx-auto">
            <BackButton to="/listings" />
            
            <h1 className="text-2xl md:text-3xl font-bold mb-6">
              {t('createListing')}
            </h1>
            
            <ListingForm onCancel={() => navigate('/listings')} />
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default CreateListing;
