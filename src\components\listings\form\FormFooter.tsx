
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { FormField, FormItem, FormLabel, FormControl } from '@/components/ui/form';
import { useFormContext } from './ListingFormContext';

interface FormFooterProps {
  isSubmitting: boolean;
  onCancel: () => void;
  isEdit?: boolean;
}

const FormFooter: React.FC<FormFooterProps> = ({
  isSubmitting,
  onCancel,
  isEdit = false
}) => {
  const { t } = useLanguage();
  const { form } = useFormContext();
  
  return (
    <div className="space-y-4">
      <FormField
        control={form.control}
        name="featured"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center space-x-2 space-y-0">
            <FormControl>
              <Switch 
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
            <FormLabel>{t('featured')}</FormLabel>
          </FormItem>
        )}
      />
      
      <div className="flex justify-end space-x-2 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
        >
          {t('cancel')}
        </Button>
        
        <Button
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting 
            ? t('saving')
            : isEdit 
              ? t('saveChanges')
              : t('create')
          }
        </Button>
      </div>
    </div>
  );
};

export default FormFooter;
