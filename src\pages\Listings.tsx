
import React, { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useIsMobile } from '@/hooks/use-mobile';
import ListingsGrid from '@/components/listings/ListingsGrid';
import GridViewToggle, { GridViewType } from '@/components/listings/GridViewToggle';
import ListingsPagination from '@/components/listings/ListingsPagination';
import { useListingsPage } from '@/hooks/listings/useListingsPage';
import ListingsSearchBar from '@/components/listings/ListingsSearchBar';
import ListingsPageHeader from '@/components/listings/ListingsPageHeader';
import ListingsFilter from '@/components/listings/ListingsFilter';
import SavedSearchManager from '@/components/listings/SavedSearchManager';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronUp, FilterIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/hooks/useLanguage';
import i18next from '@/utils/i18n';

const Listings: React.FC = () => {
  const isMobile = useIsMobile();
  const [filtersOpen, setFiltersOpen] = useState(false);
  const { t, language } = useLanguage();

  const [gridView, setGridView] = useState<GridViewType>('multiple');

  // Load grid view from localStorage on mount
  useEffect(() => {
    try {
      const savedView = localStorage.getItem('gridView');
      if (savedView === 'single' || savedView === 'multiple' || savedView === 'list') {
        setGridView(savedView as GridViewType);
      } else if (isMobile) {
        // Default to single view on mobile devices
        setGridView('single');
      }
    } catch (error) {
      console.error('Error loading grid view from localStorage:', error);
    }
  }, [isMobile]);

  // Handle grid view change and save to localStorage
  const handleGridViewChange = (view: GridViewType) => {
    setGridView(view);
    try {
      localStorage.setItem('gridView', view);
    } catch (error) {
      console.error('Error saving grid view to localStorage:', error);
    }
  };

  // Debug translations
  useEffect(() => {
    console.log('Listings page language:', language);
    console.log('Filter translation:', t('listings.filter'));
    console.log('Hide Filters translation:', t('listings.hideFilters'));
    console.log('Search and Filters translation:', t('listings.searchAndFilters'));

    // Direct i18next check
    if (i18next.isInitialized) {
      console.log('i18next filter:', i18next.t('listings.filter'));
    }
  }, [t, language]);

  const {
    searchTerm,
    setSearchTerm,
    sortBy,
    setSortBy,
    listings,
    filteredListings,
    paginatedListings,
    isLoading,
    currentPage,
    totalPages,
    handlePageChange,
    handleRefresh,
    handleFilterApply,
    handleCreateTestListings,
    activeFilters,
    updateUrlParams
  } = useListingsPage();

  // Force re-render on language change
  useEffect(() => {
    const handleLanguageChange = () => {
      // Force re-render by updating state
      setFiltersOpen(prev => prev);
    };

    document.addEventListener('languageChanged', handleLanguageChange);

    return () => {
      document.removeEventListener('languageChanged', handleLanguageChange);
    };
  }, []);

  // These are the translations we need to ensure are working
  const filterText = t('listings.filter');
  const hideFiltersText = t('listings.hideFilters');
  const searchAndFiltersText = t('listings.searchAndFilters');

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-grow">
        <div className="bg-secondary/50 py-6">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="w-full">
              <Collapsible
                open={!isMobile || filtersOpen}
                onOpenChange={setFiltersOpen}
                className="w-full"
              >
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold">
                    {isMobile ? (filtersOpen ? hideFiltersText : filterText) : searchAndFiltersText}
                  </h2>
                  {isMobile && (
                    <CollapsibleTrigger asChild>
                      <Button variant="outline" size="sm">
                        <FilterIcon className="h-4 w-4 mr-2" />
                        {filtersOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                      </Button>
                    </CollapsibleTrigger>
                  )}
                </div>
                <CollapsibleContent className={`${isMobile ? '' : 'block'}`}>
                  <ListingsSearchBar
                    searchTerm={searchTerm}
                    onSearchChange={(value) => {
                      setSearchTerm(value);
                      updateUrlParams(1);
                    }}
                    sortBy={sortBy}
                    onSortChange={setSortBy}
                    onFilterApply={handleFilterApply}
                    onFilterReset={() => handleFilterApply({})}
                    onRefresh={handleRefresh}
                    filters={activeFilters}
                  />
                </CollapsibleContent>
              </Collapsible>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <ListingsPageHeader
            totalCount={listings.length}
            filteredCount={filteredListings.length}
            isLoading={isLoading}
            currentPage={currentPage}
            totalPages={totalPages}
            onCreateTestListings={handleCreateTestListings}
          />

          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <div className="lg:hidden">
                <ListingsFilter
                  onFilterApply={handleFilterApply}
                  onFilterReset={() => handleFilterApply({})}
                />
              </div>

              <SavedSearchManager
                onApplySearch={(searchTerm, filters) => {
                  setSearchTerm(searchTerm);
                  handleFilterApply(filters);
                }}
              />
            </div>

            <div className="ml-auto">
              <GridViewToggle
                currentView={gridView}
                onChange={handleGridViewChange}
              />
            </div>
          </div>

          <ListingsGrid
            listings={paginatedListings}
            isLoading={isLoading}
            gridView={gridView}
          />

          {!isLoading && (
            <ListingsPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Listings;
