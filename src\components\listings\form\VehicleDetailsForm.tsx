
import React, { useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Combobox } from '@/components/ui/combobox';
import { carMakes, carModelsByMake, defaultModels } from '@/utils/dictionaries';
import { useFormContext } from './ListingFormContext';

const VehicleDetailsForm: React.FC = () => {
  const { t } = useLanguage();
  const { 
    form,
    customMake,
    setCustomMake,
    customModel,
    setCustomModel
  } = useFormContext();
  
  // Create year options from 1950 to current year
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: currentYear - 1949 }, (_, i) => (currentYear - i).toString());
  
  // Set model options based on selected make
  const [modelOptions, setModelOptions] = React.useState<string[]>([]);
  const make = form.watch('make');
  
  useEffect(() => {
    if (make) {
      const models = carModelsByMake[make] || defaultModels;
      setModelOptions(models);
    } else {
      setModelOptions([]);
    }
  }, [make]);
  
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="year"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('year')} *</FormLabel>
              <FormControl>
                <Combobox
                  value={field.value.toString()}
                  setValue={(value) => field.onChange(Number(value))}
                  options={yearOptions}
                  placeholder={t('selectYear')}
                  allowCustomValue
                  onCustomValueChange={(value) => field.onChange(Number(value))}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="make"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('make')} *</FormLabel>
              <FormControl>
                <Combobox
                  value={field.value}
                  setValue={field.onChange}
                  options={carMakes}
                  placeholder={t('selectMake')}
                  allowCustomValue
                  onCustomValueChange={setCustomMake}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      
      <FormField
        control={form.control}
        name="model"
        render={({ field }) => (
          <FormItem>
            <FormLabel>{t('model')} *</FormLabel>
            <FormControl>
              <Combobox
                value={field.value}
                setValue={field.onChange}
                options={modelOptions}
                placeholder={t('selectModel')}
                allowCustomValue
                onCustomValueChange={setCustomModel}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default VehicleDetailsForm;
