
import React, { useState, useEffect } from 'react';
import { useListings } from '@/hooks/useListings';
import { toast } from 'sonner';
import ListingSearch from './listings/ListingSearch';
import ListingFilters from './listings/ListingFilters';
import ListingTable from './listings/ListingTable';
import AdminListingsPagination from './listings/AdminListingsPagination';
import EmptyState from './listings/EmptyState';
import LoadingState from './listings/LoadingState';
import { useLanguage } from '@/hooks/useLanguage';
import { paginateListings } from './listings/utils';

const AdminListings: React.FC = () => {
  const { t } = useLanguage();
  const { listings, isLoading, fetchListings, featureListing, deleteListing, setVipStatus, setPaidStatus } = useListings();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredListings, setFilteredListings] = useState(listings);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Показывать по 10 объявлений на странице
  
  // Filters
  const [featureFilter, setFeatureFilter] = useState('all');
  const [vipFilter, setVipFilter] = useState('all');
  const [paidFilter, setPaidFilter] = useState('all');
  
  useEffect(() => {
    fetchListings();
  }, [fetchListings]);
  
  useEffect(() => {
    let results = [...listings];
    
    if (searchTerm) {
      const lowerCaseSearch = searchTerm.toLowerCase();
      results = results.filter(listing => 
        listing.title?.toLowerCase().includes(lowerCaseSearch) ||
        listing.make?.toLowerCase().includes(lowerCaseSearch) ||
        listing.model?.toLowerCase().includes(lowerCaseSearch) ||
        listing.location?.toLowerCase().includes(lowerCaseSearch)
      );
    }
    
    // Apply featured filter
    if (featureFilter !== 'all') {
      const isFeatured = featureFilter === 'featured';
      results = results.filter(listing => listing.featured === isFeatured);
    }

    // Apply VIP filter
    if (vipFilter !== 'all') {
      const isVip = vipFilter === 'vip';
      results = results.filter(listing => listing.vip_status === isVip);
    }
    
    // Apply paid filter
    if (paidFilter !== 'all') {
      const isPaid = paidFilter === 'paid';
      results = results.filter(listing => listing.paid_status === isPaid);
    }
    
    setFilteredListings(results);
    // Reset to first page when filters change
    setCurrentPage(1);
  }, [listings, searchTerm, featureFilter, vipFilter, paidFilter]);
  
  // Get paginated listings
  const { currentItems: paginatedListings, totalPages } = paginateListings(
    filteredListings,
    currentPage,
    itemsPerPage
  );
  
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };
  
  const handleFeatureToggle = async (id: string, currentStatus: boolean) => {
    try {
      await featureListing(id, !currentStatus);
      toast.success(currentStatus 
        ? t('admin.removedFromFeatured')
        : t('admin.addedToFeatured'), {
          description: currentStatus 
            ? t('admin.removedFromFeaturedDesc')
            : t('admin.addedToFeaturedDesc')
        }
      );
    } catch (error) {
      console.error('Error toggling feature status:', error);
      toast.error(t('admin.featureUpdateError'), {
        description: t('admin.featureUpdateErrorDesc')
      });
    }
  };

  const handleVipStatusToggle = async (id: string, currentStatus: boolean) => {
    try {
      await setVipStatus(id, !currentStatus);
      toast.success(currentStatus 
        ? t('admin.vipStatusRemoved')
        : t('admin.vipStatusAdded'), {
          description: currentStatus 
            ? t('admin.vipStatusRemovedDesc')
            : t('admin.vipStatusAddedDesc')
        }
      );
    } catch (error) {
      console.error('Error toggling VIP status:', error);
      toast.error(t('admin.vipStatusUpdateError'), {
        description: t('admin.vipStatusUpdateErrorDesc')
      });
    }
  };
  
  const handlePaidStatusToggle = async (id: string, currentStatus: boolean) => {
    try {
      await setPaidStatus(id, !currentStatus);
      toast.success(t('admin.paidStatusUpdated'), {
        description: t('admin.paidStatusUpdatedDesc')
      });
    } catch (error) {
      console.error('Error toggling paid status:', error);
      toast.error(t('admin.paidStatusUpdateError'), {
        description: t('admin.paidStatusUpdateErrorDesc')
      });
    }
  };
  
  const handleDeleteListing = async (id: string) => {
    try {
      await deleteListing(id);
      toast.success(t('admin.listingDeletedSuccess'), {
        description: t('admin.listingDeletedSuccessDesc')
      });
    } catch (error) {
      console.error('Error deleting listing:', error);
      toast.error(t('admin.listingDeleteError'), {
        description: t('admin.listingDeleteErrorDesc')
      });
    }
  };
  
  const handleBlockListing = async (id: string, isBlocked: boolean) => {
    try {
      // For this MVP, we'll just toggle featured off as a form of "blocking"
      // In a real implementation, you would probably have a separate "blocked" status field
      if (!isBlocked) {
        await featureListing(id, false);
      }
      toast.success(t('admin.listingBlocked'), {
        description: t('admin.listingBlockedDesc')
      });
    } catch (error) {
      console.error('Error blocking listing:', error);
      toast.error(t('admin.blockError'), {
        description: t('admin.blockErrorDesc')
      });
    }
  };
  
  const handleRefresh = async () => {
    toast.loading(t('admin.refreshingListings'));
    await fetchListings();
    toast.success(t('admin.listingsRefreshed'));
  };
  
  const clearFilters = () => {
    setSearchTerm('');
    setFeatureFilter('all');
    setVipFilter('all');
    setPaidFilter('all');
  };
  
  if (isLoading) {
    return <LoadingState />;
  }
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row gap-4 justify-between">
        <ListingSearch 
          searchTerm={searchTerm} 
          setSearchTerm={setSearchTerm} 
        />
        
        <ListingFilters 
          featureFilter={featureFilter} 
          setFeatureFilter={setFeatureFilter}
          vipFilter={vipFilter}
          setVipFilter={setVipFilter}
          paidFilter={paidFilter}
          setPaidFilter={setPaidFilter}
          onRefresh={handleRefresh} 
        />
      </div>
      
      {filteredListings.length > 0 ? (
        <>
          <ListingTable 
            listings={paginatedListings} 
            onFeatureToggle={handleFeatureToggle}
            onVipStatusToggle={handleVipStatusToggle}
            onPaidStatusToggle={handlePaidStatusToggle}
            onDelete={handleDeleteListing} 
            onBlockToggle={handleBlockListing}
          />
          
          <div className="flex justify-center mt-6">
            <AdminListingsPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
          
          <div className="text-center text-sm text-muted-foreground mt-2">
            {t('page')} {currentPage} {t('of')} {totalPages} • {t('admin.showing')} {paginatedListings.length} {t('admin.of')} {filteredListings.length} {t('admin.items')}
          </div>
        </>
      ) : (
        <EmptyState onClearFilters={clearFilters} />
      )}
    </div>
  );
};

export default AdminListings;
