
import { Database } from '@/integrations/supabase/types';

// Extend the base type from Supabase with our additional fields
export type ListingRow = Database['public']['Tables']['listings']['Row'];

// Include our additional fields in the NewListing type
export interface NewListing extends Omit<ListingRow, 'id' | 'created_at' | 'updated_at'> {
  body_type?: string | null;
  fuel_type?: string | null;
  transmission?: string | null;
  color?: string | null;
  mileage?: number | null;
}

// Extended Listing type that includes our client-side properties
export interface Listing extends ListingRow {
  vip_status?: boolean;
  paid_status?: boolean;
  status?: 'pending' | 'approved' | 'rejected';
  body_type?: string;
  fuel_type?: string;
  transmission?: string;
  color?: string;
  mileage?: number;
}

export interface UseListingsReturn {
  listings: Listing[];
  featuredListings: Listing[];
  isLoading: boolean;
  error: Error | null;
  fetchListings: () => Promise<void>;
  fetchListingById: (id: string) => Promise<Listing | null>;
  createListing: (listing: Omit<NewListing, 'user_id'>) => Promise<Listing>;
  updateListing: (id: string, listing: Partial<Listing>) => Promise<Listing>;
  deleteListing: (id: string) => Promise<void>;
  featureListing: (id: string, featured: boolean) => Promise<void>;
  setVipStatus: (id: string, vipStatus: boolean) => Promise<void>;
  setPaidStatus: (id: string, paidStatus: boolean) => Promise<void>;
  createTestListings: () => Promise<void>;
  updateListingStatus?: (id: string, status: 'pending' | 'approved' | 'rejected') => Promise<void>;
}
