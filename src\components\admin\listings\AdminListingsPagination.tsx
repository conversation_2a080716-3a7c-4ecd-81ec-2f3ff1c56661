
import React from 'react';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { useLanguage } from '@/hooks/useLanguage';

interface AdminListingsPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const AdminListingsPagination: React.FC<AdminListingsPaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
}) => {
  const { t } = useLanguage();
  
  // Don't render pagination if there's only one page
  if (totalPages <= 1) return null;
  
  // Generate pagination numbers efficiently
  const getPaginationRange = () => {
    const delta = 1; // How many pages to show before and after current page
    let range = [];
    
    for (let i = Math.max(2, currentPage - delta); 
         i <= Math.min(totalPages - 1, currentPage + delta); 
         i++) {
      range.push(i);
    }
    
    // Add first page if not already included
    if (range.length === 0 || range[0] !== 1) {
      range.unshift(1);
    }
    
    // Add last page if not already included and if totalPages > 1
    if (totalPages > 1 && range[range.length - 1] !== totalPages) {
      range.push(totalPages);
    }
    
    return range;
  };
  
  const paginationRange = getPaginationRange();
  
  return (
    <Pagination className="mt-4">
      <PaginationContent>
        {currentPage > 1 && (
          <PaginationItem>
            <PaginationPrevious 
              onClick={() => onPageChange(currentPage - 1)} 
              className="cursor-pointer"
              aria-label={t('previous')}
            />
          </PaginationItem>
        )}
        
        {paginationRange.map((page, i, array) => (
          <React.Fragment key={page}>
            <PaginationItem>
              <PaginationLink
                isActive={page === currentPage}
                onClick={() => onPageChange(page)}
                className="cursor-pointer"
              >
                {page}
              </PaginationLink>
            </PaginationItem>
            
            {/* Add ellipsis if there's a gap */}
            {i < array.length - 1 && array[i + 1] - page > 1 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}
          </React.Fragment>
        ))}
        
        {currentPage < totalPages && (
          <PaginationItem>
            <PaginationNext 
              onClick={() => onPageChange(currentPage + 1)} 
              className="cursor-pointer"
              aria-label={t('next')}
            />
          </PaginationItem>
        )}
      </PaginationContent>
    </Pagination>
  );
};

export default AdminListingsPagination;
